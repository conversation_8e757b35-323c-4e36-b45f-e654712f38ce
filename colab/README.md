
# RemuPro 📊
### Sistema de Procesamiento de Remuneraciones SEP - PIE - NORMAL

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://www.python.org/)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15.11-blue.svg)](https://riverbankcomputing.com/software/pyqt/intro)
[![Maintenance](https://img.shields.io/badge/Maintained%3F-yes-green.svg)](https://github.com/leftra123/colab/graphs/commit-activity)
[![Made with Love](https://img.shields.io/badge/Made%20with-❤️-red.svg)](https://github.com/leftra123)

## 🎯 Descripción

**RemuPro** es una solución profesional para el procesamiento automatizado de remuneraciones en el sector educativo. Diseñada específicamente para manejar datos de programas **SEP**, **PIE** y **NORMAL**, esta aplicación transforma el procesamiento de planillas Excel en una tarea simple y eficiente.

## ✨ Características Principales

### 🖥️ Interfaz Usuario Amigable
- Diseño moderno e intuitivo con PyQt5
- Flujo de trabajo simplificado con solo unos clics
- Indicadores visuales de progreso en tiempo real

### 🔄 Procesamiento Avanzado
- Análisis inteligente de hojas `HORAS` y `TOTAL`
- Sistema robusto de validación de datos
- Manejo automático de errores y reintentos
- Generación automática de nombres de archivo

### 📊 Capacidades Analíticas
- Procesamiento especializado para programas SEP, PIE y NORMAL
- Cálculos precisos y verificables
- Exportación de resultados en formato Excel optimizado

## 🚀 Inicio Rápido

### Prerrequisitos
```
- Python 3.8 o superior
- Sistema operativo compatible (macOS/Windows/Linux)
- 2GB RAM mínimo recomendado
```

### Instalación

1. **Clonar el Repositorio**
```bash
git clone https://github.com/leftra123/colab.git
cd colab
```

2. **Configurar Entorno Virtual**
```bash
python -m venv venv

# Para macOS/Linux:
source venv/bin/activate

# Para Windows:
venv\Scripts\activate
```

3. **Instalar Dependencias**
```bash
pip install -r requirements.txt
```

## 💻 Uso

1. **Iniciar la Aplicación**
```bash
python main.py
```

2. **Proceso de Uso**
- Seleccione el archivo Excel de entrada
- Elija la ubicación de salida (o use la sugerencia automática)
- Haga clic en "Procesar" y observe el progreso
- Revise el archivo resultante en la ubicación especificada

## 🏗️ Arquitectura

RemuPro está construido con una arquitectura modular que permite:
- Fácil extensión de funcionalidades
- Mantenimiento simplificado
- Alta escalabilidad
- Integración con otros sistemas

## 🛠️ Tecnologías Utilizadas

<div  align="center"> <table  border="0"> <tr> <td><img  src="https://raw.githubusercontent.com/github/explore/80688e429a7d4ef2fca1e82350fe8e3517d3494d/topics/python/python.png"  width="150"  alt="Python Logo"/></td> <td><img  src="https://upload.wikimedia.org/wikipedia/commons/0/0b/Qt_logo_2016.svg"  width="150"  alt="Qt Logo"/></td> <td><img  src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/34/Microsoft_Office_Excel_%282019%E2%80%93present%29.svg/512px-Microsoft_Office_Excel_%282019%E2%80%93present%29.svg.png"  width="150"  alt="Excel Logo"/></td> </tr> </table> </div>

## 📝 Licencia

Este proyecto está licenciado bajo la Licencia MIT - vea el archivo [LICENSE](LICENSE) para más detalles.

## 👥 Contacto

**Eric Aguayo Quintriqueo**
- LinkedIn: [Ver Perfil](https://www.linkedin.com/in/eric-aguayo-quintriqueo-b36783220/)
- GitHub: [@leftra123](https://github.com/leftra123)

---
<div align="center">
  <sub>Construido con ❤️ por Eric Aguayo Quintriqueo</sub>
</div>